"""
Start the MCP server with stdio transport for Augment.

This script starts the MCP server with stdio transport which is required for Augment.
"""

import sys
import os
from pathlib import Path

# Ensure we're in the correct directory
script_dir = Path(__file__).parent.absolute()
os.chdir(script_dir)

from src.server import mcp

def main():
    """Start the MCP server with stdio transport."""
    print("Starting Cloudbeds MCP server with stdio transport...", file=sys.stderr)
    print(f"Working directory: {os.getcwd()}", file=sys.stderr)
    
    # Run with stdio transport (default)
    mcp.run()

if __name__ == "__main__":
    main()
