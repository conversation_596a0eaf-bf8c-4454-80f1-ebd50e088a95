"""
Start the MCP server.

This script starts the MCP server with HTTP transport.
"""

import sys
import os
from pathlib import Path

# Ensure we're in the correct directory
script_dir = Path(__file__).parent.absolute()
os.chdir(script_dir)

from src.server import mcp

def main():
    """Start the MCP server with HTTP transport."""
    print("Starting Cloudbeds MCP server with HTTP transport...")
    print(f"Working directory: {os.getcwd()}")
    mcp.run(
        transport="streamable-http",
        host="localhost",
        port=8000,
        path="/mcp"
    )

if __name__ == "__main__":
    main()
