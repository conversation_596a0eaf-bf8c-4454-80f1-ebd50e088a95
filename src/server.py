"""
Cloudbeds MCP server.

This module creates and configures the FastMCP server for Cloudbeds API.
Production-ready implementation with working tools only.
"""

from fastmcp import FastMCP
from typing import Dict, List, Optional, Any

# Import working tools only
from src.tools.reservations import (
    get_reservations,
    get_reservation_details,
    get_reservation_invoice,
    create_reservation,
)
from src.tools.rooms import (
    get_room_types,
    get_rooms,
)
from src.tools.guests import (
    get_guest_details,
    search_guests,
)

# Create FastMCP instance
mcp = FastMCP(name="CloudbedsMCP")

# Define dependencies for fastmcp dev command
mcp.dependencies = [
    "httpx",
    "python-dotenv",
    "fastmcp",
    "uvicorn"
]

# Register working tools only
@mcp.tool()
async def get_reservations_tool(days_back: int = 30) -> List[Dict[str, Any]]:
    """
    Retrieve reservations from Cloudbeds API.

    Args:
        days_back (int): Number of days to look back for reservations

    Returns:
        List[Dict[str, Any]]: List of reservation data
    """
    return await get_reservations(days_back=days_back)

@mcp.tool()
async def get_reservation_tool(reservation_id: str) -> Dict[str, Any]:
    """
    Retrieve detailed information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID

    Returns:
        Dict[str, Any]: Detailed reservation data
    """
    return await get_reservation_details(reservation_id=reservation_id)

@mcp.tool()
async def create_reservation_tool(reservation_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new reservation in Cloudbeds.

    Args:
        reservation_data (Dict[str, Any]): Reservation data containing:
            - firstName, lastName, email (required)
            - roomTypeID or roomName (required)
            - startDate, endDate (required, YYYY-MM-DD format)
            - adults, children (optional, defaults to 1 and 0)
            - paymentMethod (optional, 'credit' or 'noPayment')
            - country (optional, defaults to 'ES')
            - phone, address, city, postalCode (optional)

    Returns:
        Dict[str, Any]: Created reservation data with reservationID and confirmationCode
    """
    return await create_reservation(reservation_data=reservation_data)

@mcp.tool()
async def get_room_types_tool() -> List[Dict[str, Any]]:
    """
    Retrieve room types from Cloudbeds API.

    Returns:
        List[Dict[str, Any]]: List of room type data
    """
    return await get_room_types()

@mcp.tool()
async def get_rooms_tool() -> List[Dict[str, Any]]:
    """
    Retrieve rooms from Cloudbeds API.

    Returns:
        List[Dict[str, Any]]: List of room data
    """
    return await get_rooms()

# Export the MCP instance
__all__ = ["mcp"]
